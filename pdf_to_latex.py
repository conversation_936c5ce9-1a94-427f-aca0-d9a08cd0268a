from pdfminer.high_level import extract_text
import os

pdf_path = 'mcf_analytical_presentation.pdf'
tex_path = 'mcf_analytical_presentation.tex'

def pdf_to_latex(pdf_path, tex_path):
    # Extract text from PDF
    text = extract_text(pdf_path)
    # Basic LaTeX document structure
    latex_content = r"""
\\documentclass{article}
\\usepackage[utf8]{inputenc}
\\begin{document}
""" + text + "\n\\end{document}"
    # Write to .tex file
    with open(tex_path, 'w', encoding='utf-8') as f:
        f.write(latex_content)
    print(f"LaTeX file created: {tex_path}")

if __name__ == "__main__":
    if os.path.exists(pdf_path):
        pdf_to_latex(pdf_path, tex_path)
    else:
        print(f"PDF file not found: {pdf_path}")
